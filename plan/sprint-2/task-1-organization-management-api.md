# Task 1: Organization Management API

## Task Title
Implement comprehensive organization management API with multi-tenant support and role-based access control

## Context
Organizations are the foundation of the multi-tenant architecture. This API enables users to create, manage, and configure organizations while establishing the data isolation boundaries that protect client data. The organization management system must support different user roles, team structures, and proper access controls.

This task creates the core multi-tenancy functionality that all other features will build upon, ensuring proper data isolation and security across different organizations using the platform.

## Acceptance Criteria

1. **Organization CRUD Operations**
   - Create new organizations with proper initialization
   - Read organization details with role-based filtering
   - Update organization settings and configuration
   - Soft delete organizations with data retention policies

2. **Multi-tenant Data Isolation**
   - All organization data is properly isolated
   - Users can only access data from their organizations
   - Cross-organization data leaks are prevented
   - Organization switching is supported for multi-org users

3. **Role-Based Access Control**
   - Organization owners have full administrative access
   - <PERSON><PERSON> can manage organization settings and members
   - Members have limited access based on their role
   - Proper permission validation on all endpoints

4. **Organization Features**
   - Organization settings and preferences management
   - Team structure within organizations
   - Member invitation and management system
   - Activity logging for organization changes

## Dependencies
- Sprint 1: All tasks completed (Database, Auth, API Foundation)
- Organization schema tables implemented
- User authentication system functional

## Technical Requirements

### API Endpoints

**Organization Management**
- `GET /api/v1/organizations` - List user's organizations
- `POST /api/v1/organizations` - Create new organization
- `GET /api/v1/organizations/{id}` - Get organization details
- `PUT /api/v1/organizations/{id}` - Update organization
- `DELETE /api/v1/organizations/{id}` - Delete organization

**Organization Members**
- `GET /api/v1/organizations/{id}/members` - List organization members
- `POST /api/v1/organizations/{id}/members/invite` - Invite new member
- `PUT /api/v1/organizations/{id}/members/{userId}` - Update member role
- `DELETE /api/v1/organizations/{id}/members/{userId}` - Remove member

**Organization Settings**
- `GET /api/v1/organizations/{id}/settings` - Get organization settings
- `PUT /api/v1/organizations/{id}/settings` - Update organization settings

### Data Models

**Organization Creation Request**
```typescript
interface CreateOrganizationRequest {
  name: string;
  description?: string;
  website?: string;
  industry?: string;
  size?: "1-10" | "11-50" | "51-200" | "201-500" | "500+";
  timezone?: string;
  currency?: string;
}
```

**Organization Response**
```typescript
interface OrganizationResponse {
  id: string;
  name: string;
  description?: string;
  website?: string;
  industry?: string;
  size?: string;
  timezone: string;
  currency: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  memberCount: number;
  userRole: "owner" | "admin" | "member";
}
```

### Implementation Files

**File: `app/api/v1/organizations/route.ts`**
```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError } from "@/core/api/response";
import { validateJsonBody } from "@/core/api/validation";
import { getDb } from "@/core/database";
import { organizations, organizationMembers } from "@/core/database/schema";
import { eq, and } from "drizzle-orm";

const createOrganizationSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().max(1000).optional(),
  website: z.string().url().optional(),
  industry: z.string().max(100).optional(),
  size: z.enum(["1-10", "11-50", "51-200", "201-500", "500+"]).optional(),
  timezone: z.string().default("UTC"),
  currency: z.string().length(3).default("USD"),
});

// GET /api/v1/organizations - List user's organizations
export const GET = withApiAuth(async (request, { user }) => {
  const db = getDb();
  
  const userOrganizations = await db
    .select({
      id: organizations.id,
      name: organizations.name,
      description: organizations.description,
      website: organizations.website,
      industry: organizations.industry,
      size: organizations.size,
      timezone: organizations.timezone,
      currency: organizations.currency,
      isActive: organizations.isActive,
      createdAt: organizations.createdAt,
      updatedAt: organizations.updatedAt,
      userRole: organizationMembers.role,
    })
    .from(organizations)
    .innerJoin(
      organizationMembers,
      eq(organizations.id, organizationMembers.organizationId)
    )
    .where(eq(organizationMembers.userId, user.id));

  return apiSuccess(userOrganizations);
});

// POST /api/v1/organizations - Create new organization
export const POST = withApiAuth(async (request, { user }) => {
  const validation = await validateJsonBody(request, createOrganizationSchema);
  if (!validation.success) {
    return validation.response;
  }

  const db = getDb();
  const organizationId = crypto.randomUUID();

  try {
    // Create organization
    await db.insert(organizations).values({
      id: organizationId,
      ownerId: user.id,
      ...validation.data,
    });

    // Add creator as owner
    await db.insert(organizationMembers).values({
      organizationId,
      userId: user.id,
      role: "owner",
      joinedAt: new Date(),
    });

    // Fetch created organization with user role
    const [createdOrg] = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        description: organizations.description,
        website: organizations.website,
        industry: organizations.industry,
        size: organizations.size,
        timezone: organizations.timezone,
        currency: organizations.currency,
        isActive: organizations.isActive,
        createdAt: organizations.createdAt,
        updatedAt: organizations.updatedAt,
        userRole: organizationMembers.role,
      })
      .from(organizations)
      .innerJoin(
        organizationMembers,
        eq(organizations.id, organizationMembers.organizationId)
      )
      .where(
        and(
          eq(organizations.id, organizationId),
          eq(organizationMembers.userId, user.id)
        )
      );

    return apiSuccess(createdOrg, { status: 201 });
  } catch (error) {
    console.error("Failed to create organization:", error);
    return apiError("CREATION_FAILED", "Failed to create organization", 500);
  }
}, { requireUserType: "internal" });
```

**File: `app/api/v1/organizations/[id]/route.ts`**
```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError, apiNotFound } from "@/core/api/response";
import { validateJsonBody } from "@/core/api/validation";
import { getDb } from "@/core/database";
import { organizations, organizationMembers } from "@/core/database/schema";
import { eq, and } from "drizzle-orm";

const updateOrganizationSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().max(1000).optional(),
  website: z.string().url().optional(),
  industry: z.string().max(100).optional(),
  size: z.enum(["1-10", "11-50", "51-200", "201-500", "500+"]).optional(),
  timezone: z.string().optional(),
  currency: z.string().length(3).optional(),
});

// GET /api/v1/organizations/{id} - Get organization details
export const GET = withApiAuth(async (request, { user }, { params }) => {
  const organizationId = params.id;
  const db = getDb();

  // Verify user has access to this organization
  const [organization] = await db
    .select({
      id: organizations.id,
      name: organizations.name,
      description: organizations.description,
      website: organizations.website,
      industry: organizations.industry,
      size: organizations.size,
      timezone: organizations.timezone,
      currency: organizations.currency,
      isActive: organizations.isActive,
      createdAt: organizations.createdAt,
      updatedAt: organizations.updatedAt,
      userRole: organizationMembers.role,
    })
    .from(organizations)
    .innerJoin(
      organizationMembers,
      eq(organizations.id, organizationMembers.organizationId)
    )
    .where(
      and(
        eq(organizations.id, organizationId),
        eq(organizationMembers.userId, user.id)
      )
    );

  if (!organization) {
    return apiNotFound("Organization");
  }

  return apiSuccess(organization);
});

// PUT /api/v1/organizations/{id} - Update organization
export const PUT = withApiAuth(async (request, { user }, { params }) => {
  const organizationId = params.id;
  const validation = await validateJsonBody(request, updateOrganizationSchema);
  
  if (!validation.success) {
    return validation.response;
  }

  const db = getDb();

  // Verify user has admin access
  const [membership] = await db
    .select({ role: organizationMembers.role })
    .from(organizationMembers)
    .where(
      and(
        eq(organizationMembers.organizationId, organizationId),
        eq(organizationMembers.userId, user.id)
      )
    );

  if (!membership || !["owner", "admin"].includes(membership.role)) {
    return apiError("INSUFFICIENT_PERMISSIONS", "Admin access required", 403);
  }

  try {
    await db
      .update(organizations)
      .set({
        ...validation.data,
        updatedAt: new Date(),
      })
      .where(eq(organizations.id, organizationId));

    // Fetch updated organization
    const [updatedOrg] = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        description: organizations.description,
        website: organizations.website,
        industry: organizations.industry,
        size: organizations.size,
        timezone: organizations.timezone,
        currency: organizations.currency,
        isActive: organizations.isActive,
        createdAt: organizations.createdAt,
        updatedAt: organizations.updatedAt,
        userRole: organizationMembers.role,
      })
      .from(organizations)
      .innerJoin(
        organizationMembers,
        eq(organizations.id, organizationMembers.organizationId)
      )
      .where(
        and(
          eq(organizations.id, organizationId),
          eq(organizationMembers.userId, user.id)
        )
      );

    return apiSuccess(updatedOrg);
  } catch (error) {
    console.error("Failed to update organization:", error);
    return apiError("UPDATE_FAILED", "Failed to update organization", 500);
  }
});
```

### Permission Validation Helper

**File: `core/api/permissions.ts`**
```typescript
import { getDb } from "@/core/database";
import { organizationMembers } from "@/core/database/schema";
import { eq, and } from "drizzle-orm";

export async function validateOrganizationAccess(
  userId: string,
  organizationId: string,
  requiredRoles?: string[]
): Promise<{ hasAccess: boolean; role?: string }> {
  const db = getDb();

  const [membership] = await db
    .select({ role: organizationMembers.role })
    .from(organizationMembers)
    .where(
      and(
        eq(organizationMembers.userId, userId),
        eq(organizationMembers.organizationId, organizationId)
      )
    );

  if (!membership) {
    return { hasAccess: false };
  }

  if (requiredRoles && !requiredRoles.includes(membership.role)) {
    return { hasAccess: false, role: membership.role };
  }

  return { hasAccess: true, role: membership.role };
}

export function requireOrganizationRole(roles: string[]) {
  return async (userId: string, organizationId: string) => {
    const { hasAccess, role } = await validateOrganizationAccess(
      userId,
      organizationId,
      roles
    );

    if (!hasAccess) {
      throw new Error(
        role 
          ? `Insufficient permissions. Required: ${roles.join(", ")}, Current: ${role}`
          : "Organization access denied"
      );
    }

    return role!;
  };
}
```

## Definition of Done

- [ ] Organization CRUD API endpoints are implemented and functional
- [ ] Multi-tenant data isolation prevents cross-organization access
- [ ] Role-based access control validates permissions on all endpoints
- [ ] Organization creation initializes proper member relationships
- [ ] Organization updates require appropriate permissions
- [ ] API responses include user's role within each organization
- [ ] Error handling provides clear feedback for permission issues
- [ ] Organization deletion is implemented with proper safeguards
- [ ] Member management endpoints support invitation workflows
- [ ] Organization settings can be configured per organization
- [ ] Activity logging captures all organization changes
- [ ] API endpoints are properly documented with request/response schemas
- [ ] Validation ensures data integrity for all organization operations
- [ ] Performance is optimized for multi-organization queries
- [ ] Security measures prevent unauthorized organization access
