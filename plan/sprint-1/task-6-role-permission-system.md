# Task 6: Role-Based Access Control System

## Task Title
Implement comprehensive role and permission system for multi-tenant access control

## Context
Following the platform development guidelines, all role definitions must be stored in `/core/auth/roles.ts` and all permission logic in `/core/auth/permissions.ts`. This task establishes a robust RBAC system that differentiates between internal users (freelancers, team members) and client users while supporting the multi-tenant architecture.

The system must provide granular permissions, role hierarchies, and flexible access control that can be easily extended as the platform grows. This foundation will be used by all API endpoints and UI components to enforce proper authorization.

## Acceptance Criteria

1. **Role Definition System**
   - Clear separation between internal and client user roles
   - Role hierarchy with inheritance and permission levels
   - Extensible role system for future requirements
   - Type-safe role definitions with TypeScript

2. **Permission Management**
   - Granular permissions for all platform features
   - Permission grouping by functional areas
   - Role-to-permission mapping with inheritance
   - Custom permission support for specific use cases

3. **Authorization Utilities**
   - Helper functions for permission checking
   - Role validation and comparison utilities
   - Middleware integration for API protection
   - UI component permission helpers

4. **Multi-Tenant Support**
   - Organization-specific role assignments
   - Cross-organization permission isolation
   - Client-specific access controls
   - Proper data isolation enforcement

## Dependencies
- Task 2: Better Auth Configuration
- Task 5: Centralized Configuration System
- Multi-tenant database schema
- TypeScript strict mode enabled

## Technical Requirements

### Role Definition System

**File: `core/auth/roles.ts`**
```typescript
export const USER_ROLES = {
  // Internal user roles (freelancers, team members)
  SUPER_ADMIN: "super_admin",
  ORGANIZATION_OWNER: "organization_owner", 
  ORGANIZATION_ADMIN: "organization_admin",
  ORGANIZATION_MEMBER: "organization_member",
  ORGANIZATION_VIEWER: "organization_viewer",
  
  // Client user roles (client portal access)
  CLIENT_ADMIN: "client_admin",
  CLIENT_USER: "client_user",
  CLIENT_VIEWER: "client_viewer",
} as const;

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];

export interface RoleDefinition {
  name: string;
  description: string;
  userType: "internal" | "client";
  level: number; // Higher number = more permissions
  inherits?: UserRole[];
  isSystemRole?: boolean; // Cannot be deleted or modified
}

export const ROLE_DEFINITIONS: Record<UserRole, RoleDefinition> = {
  [USER_ROLES.SUPER_ADMIN]: {
    name: "Super Admin",
    description: "Platform-wide administrative access",
    userType: "internal",
    level: 100,
    isSystemRole: true,
  },
  
  [USER_ROLES.ORGANIZATION_OWNER]: {
    name: "Organization Owner",
    description: "Full control over organization and all its data",
    userType: "internal", 
    level: 90,
    isSystemRole: true,
  },
  
  [USER_ROLES.ORGANIZATION_ADMIN]: {
    name: "Organization Admin",
    description: "Administrative access within organization",
    userType: "internal",
    level: 80,
    isSystemRole: true,
  },
  
  [USER_ROLES.ORGANIZATION_MEMBER]: {
    name: "Organization Member", 
    description: "Standard member access within organization",
    userType: "internal",
    level: 60,
    isSystemRole: true,
  },
  
  [USER_ROLES.ORGANIZATION_VIEWER]: {
    name: "Organization Viewer",
    description: "Read-only access within organization", 
    userType: "internal",
    level: 40,
    isSystemRole: true,
  },
  
  [USER_ROLES.CLIENT_ADMIN]: {
    name: "Client Admin",
    description: "Administrative access to client portal",
    userType: "client",
    level: 70,
    isSystemRole: true,
  },
  
  [USER_ROLES.CLIENT_USER]: {
    name: "Client User",
    description: "Standard client portal access",
    userType: "client", 
    level: 50,
    isSystemRole: true,
  },
  
  [USER_ROLES.CLIENT_VIEWER]: {
    name: "Client Viewer",
    description: "Read-only client portal access",
    userType: "client",
    level: 30,
    isSystemRole: true,
  },
};

// Role utility functions
export function getRoleDefinition(role: UserRole): RoleDefinition {
  return ROLE_DEFINITIONS[role];
}

export function isInternalRole(role: UserRole): boolean {
  return ROLE_DEFINITIONS[role].userType === "internal";
}

export function isClientRole(role: UserRole): boolean {
  return ROLE_DEFINITIONS[role].userType === "client";
}

export function hasHigherRole(userRole: UserRole, requiredRole: UserRole): boolean {
  return ROLE_DEFINITIONS[userRole].level >= ROLE_DEFINITIONS[requiredRole].level;
}

export function getRolesByUserType(userType: "internal" | "client"): UserRole[] {
  return Object.keys(ROLE_DEFINITIONS).filter(role => 
    ROLE_DEFINITIONS[role as UserRole].userType === userType
  ) as UserRole[];
}

export function getInternalRoles(): UserRole[] {
  return getRolesByUserType("internal");
}

export function getClientRoles(): UserRole[] {
  return getRolesByUserType("client");
}
```

### Permission System

**File: `core/auth/permissions.ts`**
```typescript
import { UserRole, USER_ROLES, ROLE_DEFINITIONS } from "./roles";

export const PERMISSIONS = {
  // Organization permissions
  ORGANIZATION_READ: "organization:read",
  ORGANIZATION_WRITE: "organization:write", 
  ORGANIZATION_DELETE: "organization:delete",
  ORGANIZATION_MANAGE_MEMBERS: "organization:manage_members",
  ORGANIZATION_MANAGE_SETTINGS: "organization:manage_settings",
  
  // User permissions
  USER_READ: "user:read",
  USER_WRITE: "user:write",
  USER_DELETE: "user:delete",
  USER_INVITE: "user:invite",
  USER_MANAGE_ROLES: "user:manage_roles",
  
  // Client permissions
  CLIENT_READ: "client:read",
  CLIENT_WRITE: "client:write", 
  CLIENT_DELETE: "client:delete",
  CLIENT_INVITE: "client:invite",
  CLIENT_PORTAL_ACCESS: "client:portal_access",
  
  // Project permissions
  PROJECT_READ: "project:read",
  PROJECT_WRITE: "project:write",
  PROJECT_DELETE: "project:delete",
  PROJECT_MANAGE_TASKS: "project:manage_tasks",
  PROJECT_MANAGE_TEAM: "project:manage_team",
  
  // Task permissions
  TASK_READ: "task:read",
  TASK_WRITE: "task:write",
  TASK_DELETE: "task:delete",
  TASK_ASSIGN: "task:assign",
  TASK_TIME_TRACK: "task:time_track",
  
  // Invoice permissions
  INVOICE_READ: "invoice:read",
  INVOICE_WRITE: "invoice:write",
  INVOICE_DELETE: "invoice:delete", 
  INVOICE_SEND: "invoice:send",
  INVOICE_PAYMENT: "invoice:payment",
  
  // Financial permissions
  FINANCIAL_READ: "financial:read",
  FINANCIAL_WRITE: "financial:write",
  FINANCIAL_REPORTS: "financial:reports",
} as const;

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];

export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [USER_ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS),
  
  [USER_ROLES.ORGANIZATION_OWNER]: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.ORGANIZATION_WRITE,
    PERMISSIONS.ORGANIZATION_DELETE,
    PERMISSIONS.ORGANIZATION_MANAGE_MEMBERS,
    PERMISSIONS.ORGANIZATION_MANAGE_SETTINGS,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_WRITE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_INVITE,
    PERMISSIONS.USER_MANAGE_ROLES,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.CLIENT_WRITE,
    PERMISSIONS.CLIENT_DELETE,
    PERMISSIONS.CLIENT_INVITE,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.PROJECT_WRITE,
    PERMISSIONS.PROJECT_DELETE,
    PERMISSIONS.PROJECT_MANAGE_TASKS,
    PERMISSIONS.PROJECT_MANAGE_TEAM,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.TASK_WRITE,
    PERMISSIONS.TASK_DELETE,
    PERMISSIONS.TASK_ASSIGN,
    PERMISSIONS.TASK_TIME_TRACK,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.INVOICE_WRITE,
    PERMISSIONS.INVOICE_DELETE,
    PERMISSIONS.INVOICE_SEND,
    PERMISSIONS.INVOICE_PAYMENT,
    PERMISSIONS.FINANCIAL_READ,
    PERMISSIONS.FINANCIAL_WRITE,
    PERMISSIONS.FINANCIAL_REPORTS,
  ],
  
  [USER_ROLES.ORGANIZATION_ADMIN]: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.ORGANIZATION_WRITE,
    PERMISSIONS.ORGANIZATION_MANAGE_MEMBERS,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_WRITE,
    PERMISSIONS.USER_INVITE,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.CLIENT_WRITE,
    PERMISSIONS.CLIENT_INVITE,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.PROJECT_WRITE,
    PERMISSIONS.PROJECT_MANAGE_TASKS,
    PERMISSIONS.PROJECT_MANAGE_TEAM,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.TASK_WRITE,
    PERMISSIONS.TASK_ASSIGN,
    PERMISSIONS.TASK_TIME_TRACK,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.INVOICE_WRITE,
    PERMISSIONS.INVOICE_SEND,
    PERMISSIONS.INVOICE_PAYMENT,
    PERMISSIONS.FINANCIAL_READ,
    PERMISSIONS.FINANCIAL_WRITE,
    PERMISSIONS.FINANCIAL_REPORTS,
  ],
  
  [USER_ROLES.ORGANIZATION_MEMBER]: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.USER_READ,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.PROJECT_WRITE,
    PERMISSIONS.PROJECT_MANAGE_TASKS,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.TASK_WRITE,
    PERMISSIONS.TASK_TIME_TRACK,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.FINANCIAL_READ,
  ],
  
  [USER_ROLES.ORGANIZATION_VIEWER]: [
    PERMISSIONS.ORGANIZATION_READ,
    PERMISSIONS.USER_READ,
    PERMISSIONS.CLIENT_READ,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.FINANCIAL_READ,
  ],
  
  [USER_ROLES.CLIENT_ADMIN]: [
    PERMISSIONS.CLIENT_PORTAL_ACCESS,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.INVOICE_READ,
    PERMISSIONS.USER_READ,
  ],
  
  [USER_ROLES.CLIENT_USER]: [
    PERMISSIONS.CLIENT_PORTAL_ACCESS,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.TASK_READ,
    PERMISSIONS.INVOICE_READ,
  ],
  
  [USER_ROLES.CLIENT_VIEWER]: [
    PERMISSIONS.CLIENT_PORTAL_ACCESS,
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.INVOICE_READ,
  ],
};

// Permission checking functions
export function hasPermission(
  userRole: UserRole,
  permission: Permission,
  customPermissions?: Permission[]
): boolean {
  // Check custom permissions first
  if (customPermissions?.includes(permission)) {
    return true;
  }
  
  // Check role-based permissions
  const rolePermissions = ROLE_PERMISSIONS[userRole];
  return rolePermissions?.includes(permission) || false;
}

export function hasAnyPermission(
  userRole: UserRole,
  permissions: Permission[],
  customPermissions?: Permission[]
): boolean {
  return permissions.some(permission => 
    hasPermission(userRole, permission, customPermissions)
  );
}

export function hasAllPermissions(
  userRole: UserRole,
  permissions: Permission[],
  customPermissions?: Permission[]
): boolean {
  return permissions.every(permission => 
    hasPermission(userRole, permission, customPermissions)
  );
}

export function requirePermission(permission: Permission) {
  return (userRole: UserRole, customPermissions?: Permission[]) => {
    if (!hasPermission(userRole, permission, customPermissions)) {
      throw new Error(`Permission denied: ${permission}`);
    }
  };
}

export function getPermissionsForRole(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role] || [];
}

export function getPermissionsByCategory(category: string): Permission[] {
  return Object.values(PERMISSIONS).filter(permission => 
    permission.startsWith(`${category}:`)
  );
}
```

## Definition of Done

- [ ] Role definition system is implemented in `/core/auth/roles.ts`
- [ ] Permission system is implemented in `/core/auth/permissions.ts`
- [ ] Clear separation between internal and client user roles
- [ ] Role hierarchy with proper permission levels is functional
- [ ] Permission checking utilities work correctly
- [ ] Type-safe role and permission definitions with TypeScript
- [ ] Role-to-permission mapping is comprehensive and accurate
- [ ] Multi-tenant support with organization-specific roles
- [ ] Authorization utilities are available for API and UI use
- [ ] Role validation and comparison functions work correctly
- [ ] Custom permission support is implemented
- [ ] Permission grouping by functional areas is organized
- [ ] System roles are protected from modification
- [ ] Role inheritance and permission levels are enforced
- [ ] Documentation explains how to add new roles and permissions
