# Task 2: Better Auth Configuration and Setup

## Task Title
Configure Better Auth with unified authentication system supporting both internal and client users

## Context
Better Auth will serve as the foundation for our unified authentication system, handling both internal users (freelancers, team members) and client users (client portal access) through a single authentication flow. This approach simplifies user management while maintaining proper data isolation through the user_type field and organization-based multi-tenancy.

The configuration must support email/password authentication, session management, and prepare for future OAuth providers while integrating seamlessly with our Drizzle ORM schema.

## Acceptance Criteria

1. **Authentication Setup**
   - Better Auth is properly configured with Drizzle adapter
   - Email/password authentication is functional
   - Session management works correctly
   - User registration and login flows are operational

2. **Unified User System**
   - Single users table handles both internal and client users
   - user_type field properly differentiates user types
   - Organization-based data isolation is enforced
   - Client users are properly linked to their client records

3. **API Integration**
   - Authentication API routes are configured
   - Session validation middleware is implemented
   - Protected routes require authentication
   - User context is available in API handlers

4. **Security Features**
   - Password hashing is properly implemented
   - Session tokens are secure and properly managed
   - CSRF protection is enabled
   - Rate limiting is configured for auth endpoints

## Dependencies
- Task 1: Database Schema Implementation (must be completed)
- Drizzle ORM configuration
- Next.js App Router setup

## Technical Requirements

### Better Auth Configuration

**File: `core/auth/server.ts`**
```typescript
import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { getDb } from "@database";

export const auth = betterAuth({
  database: drizzleAdapter(getDb(), {
    provider: "pg",
    schema: {
      // Map to our existing schema
      user: users,
      session: sessions,
      account: accounts,
      verification: verifications,
      twoFactor: twoFactors,
    },
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  advanced: {
    generateId: () => crypto.randomUUID(),
  },
});
```

**File: `core/auth/client.ts`**
```typescript
import { createAuthClient } from "better-auth/client";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_BETTER_AUTH_URL,
});
```

### API Routes

**File: `app/api/auth/[...auth]/route.ts`**
```typescript
import { auth } from "@/core/auth/server";

export const { GET, POST } = auth.handler;
```

### Authentication Middleware

**File: `core/auth/middleware.ts`**
```typescript
import { NextRequest, NextResponse } from "next/server";
import { auth } from "./server";

export async function authMiddleware(request: NextRequest) {
  const session = await auth.api.getSession({
    headers: request.headers,
  });

  // Add user context to request headers
  if (session) {
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set("x-user-id", session.user.id);
    requestHeaders.set("x-user-type", session.user.userType);
    requestHeaders.set("x-organization-id", session.user.organizationId || "");
    
    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }

  return NextResponse.next();
}
```

### User Registration Flow

**Internal User Registration**
```typescript
// For freelancers and team members
const registerInternalUser = async (data: {
  name: string;
  email: string;
  password: string;
  organizationId?: string;
}) => {
  return await auth.api.signUpEmail({
    email: data.email,
    password: data.password,
    name: data.name,
    callbackURL: "/dashboard",
    body: {
      userType: "internal",
      organizationId: data.organizationId,
    },
  });
};
```

**Client User Registration**
```typescript
// For client portal users
const registerClientUser = async (data: {
  name: string;
  email: string;
  password: string;
  clientId: string;
  organizationId: string;
}) => {
  return await auth.api.signUpEmail({
    email: data.email,
    password: data.password,
    name: data.name,
    callbackURL: "/client-portal",
    body: {
      userType: "client",
      clientId: data.clientId,
      organizationId: data.organizationId,
    },
  });
};
```

### Session Management

**File: `core/auth/session.ts`**
```typescript
import { auth } from "./server";
import { headers } from "next/headers";

export async function getSession() {
  return await auth.api.getSession({
    headers: await headers(),
  });
}

export async function requireAuth() {
  const session = await getSession();
  if (!session) {
    throw new Error("Authentication required");
  }
  return session;
}

export async function requireInternalUser() {
  const session = await requireAuth();
  if (session.user.userType !== "internal") {
    throw new Error("Internal user access required");
  }
  return session;
}

export async function requireClientUser() {
  const session = await requireAuth();
  if (session.user.userType !== "client") {
    throw new Error("Client user access required");
  }
  return session;
}
```

### Database Hooks

**File: `core/auth/hooks.ts`**
```typescript
import { auth } from "./server";

// Add database hooks for user creation
auth.hooks.after([
  {
    matcher: (context) => context.path === "/sign-up/email",
    handler: async (context) => {
      const { user } = context;
      
      // Log user creation activity
      await db.insert(activityLogs).values({
        organizationId: user.organizationId,
        userId: user.id,
        entityType: "user",
        entityId: user.id,
        action: "create",
        newValues: JSON.stringify({
          name: user.name,
          email: user.email,
          userType: user.userType,
        }),
      });
    },
  },
]);
```

### Environment Variables

Required environment variables:
```env
BETTER_AUTH_SECRET=your-secret-key-here
BETTER_AUTH_URL=http://localhost:3000
NEXT_PUBLIC_BETTER_AUTH_URL=http://localhost:3000
```

### Protected Route Helper

**File: `core/auth/protected.ts`**
```typescript
import { NextRequest } from "next/server";
import { auth } from "./server";

export async function withAuth(
  handler: (request: NextRequest, context: { user: any }) => Promise<Response>,
  options?: {
    requireUserType?: "internal" | "client";
    requireOrganization?: boolean;
  }
) {
  return async (request: NextRequest) => {
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      return new Response("Unauthorized", { status: 401 });
    }

    if (options?.requireUserType && session.user.userType !== options.requireUserType) {
      return new Response("Forbidden", { status: 403 });
    }

    if (options?.requireOrganization && !session.user.organizationId) {
      return new Response("Organization required", { status: 403 });
    }

    return handler(request, { user: session.user });
  };
}
```

## Definition of Done

- [ ] Better Auth is configured with Drizzle adapter
- [ ] Email/password authentication works for both user types
- [ ] User registration creates proper user records with correct user_type
- [ ] Session management is functional and secure
- [ ] Authentication API routes are accessible
- [ ] Protected route middleware is implemented
- [ ] User context is properly passed to API handlers
- [ ] Password hashing and security features are enabled
- [ ] Session validation works correctly
- [ ] Database hooks log authentication activities
- [ ] Environment variables are properly configured
- [ ] Authentication flows are tested and working
- [ ] Error handling for authentication failures is implemented
- [ ] Rate limiting is configured for auth endpoints
- [ ] CSRF protection is enabled and functional
