# Task 3: Core API Foundation and Structure

## Task Title
Establish the foundational API structure with authentication, error handling, and response patterns

## Context
This task creates the core API infrastructure that all subsequent endpoints will build upon. We need to establish consistent patterns for request handling, authentication, error responses, and data validation that will be used throughout the platform.

The API foundation must support both internal users (dashboard/admin interfaces) and client users (client portal), with proper data isolation and security measures. This includes setting up middleware, response helpers, and validation patterns that ensure consistency across all endpoints.

## Acceptance Criteria

1. **API Structure**
   - Consistent API route organization under `/api/v1/`
   - Standardized request/response patterns
   - Proper HTTP status codes and error messages
   - API versioning support for future compatibility

2. **Authentication Integration**
   - All protected endpoints validate user sessions
   - User context is available in all API handlers
   - Organization-based data isolation is enforced
   - Role-based access control foundations are in place

3. **Error Handling**
   - Centralized error handling with consistent error responses
   - Proper logging of API errors and activities
   - Validation errors are properly formatted
   - Database errors are handled gracefully

4. **Response Patterns**
   - Consistent JSON response structure
   - Pagination support for list endpoints
   - Metadata included in responses (timestamps, counts, etc.)
   - Success and error responses follow the same pattern

## Dependencies
- Task 1: Database Schema Implementation
- Task 2: Better Auth Configuration
- Next.js App Router setup

## Technical Requirements

### API Route Structure

```
app/api/v1/
├── auth/
│   ├── login/route.ts
│   ├── register/route.ts
│   └── logout/route.ts
├── organizations/
│   ├── route.ts
│   └── [id]/route.ts
├── users/
│   ├── route.ts
│   └── [id]/route.ts
├── clients/
│   ├── route.ts
│   └── [id]/route.ts
├── projects/
│   ├── route.ts
│   └── [id]/route.ts
└── health/route.ts
```

### Core API Types

**File: `core/api/types.ts`**
```typescript
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export interface ApiRequest {
  user: {
    id: string;
    email: string;
    userType: "internal" | "client";
    organizationId?: string;
    clientId?: string;
  };
  organizationId: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}
```

### Response Helpers

**File: `core/api/response.ts`**
```typescript
import { NextResponse } from "next/server";
import { ApiResponse } from "./types";

export function apiSuccess<T>(
  data: T,
  meta?: ApiResponse<T>["meta"]
): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      requestId: crypto.randomUUID(),
      ...meta,
    },
  });
}

export function apiError(
  code: string,
  message: string,
  status: number = 400,
  details?: any
): NextResponse<ApiResponse> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code,
        message,
        details,
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: crypto.randomUUID(),
      },
    },
    { status }
  );
}

export function apiValidationError(
  errors: Record<string, string[]>
): NextResponse<ApiResponse> {
  return apiError(
    "VALIDATION_ERROR",
    "Validation failed",
    400,
    { fields: errors }
  );
}

export function apiNotFound(
  resource: string = "Resource"
): NextResponse<ApiResponse> {
  return apiError(
    "NOT_FOUND",
    `${resource} not found`,
    404
  );
}

export function apiUnauthorized(
  message: string = "Authentication required"
): NextResponse<ApiResponse> {
  return apiError(
    "UNAUTHORIZED",
    message,
    401
  );
}

export function apiForbidden(
  message: string = "Access denied"
): NextResponse<ApiResponse> {
  return apiError(
    "FORBIDDEN",
    message,
    403
  );
}
```

### API Middleware

**File: `core/api/middleware.ts`**
```typescript
import { NextRequest } from "next/server";
import { auth } from "@/core/auth/server";
import { apiUnauthorized, apiForbidden } from "./response";
import { ApiRequest } from "./types";

export async function withApiAuth(
  handler: (request: NextRequest, context: ApiRequest) => Promise<Response>,
  options?: {
    requireUserType?: "internal" | "client";
    requireOrganization?: boolean;
  }
) {
  return async (request: NextRequest) => {
    try {
      const session = await auth.api.getSession({
        headers: request.headers,
      });

      if (!session) {
        return apiUnauthorized();
      }

      const { user } = session;

      // Check user type requirement
      if (options?.requireUserType && user.userType !== options.requireUserType) {
        return apiForbidden("Invalid user type for this endpoint");
      }

      // Check organization requirement
      if (options?.requireOrganization && !user.organizationId) {
        return apiForbidden("Organization membership required");
      }

      const context: ApiRequest = {
        user: {
          id: user.id,
          email: user.email,
          userType: user.userType,
          organizationId: user.organizationId,
          clientId: user.clientId,
        },
        organizationId: user.organizationId || "",
      };

      return await handler(request, context);
    } catch (error) {
      console.error("API Auth Error:", error);
      return apiError(
        "INTERNAL_ERROR",
        "Authentication failed",
        500
      );
    }
  };
}
```

### Validation Helpers

**File: `core/api/validation.ts`**
```typescript
import { z } from "zod";
import { apiValidationError } from "./response";

export function validateRequest<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; response: Response } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string[]> = {};
      
      error.errors.forEach((err) => {
        const path = err.path.join(".");
        if (!fieldErrors[path]) {
          fieldErrors[path] = [];
        }
        fieldErrors[path].push(err.message);
      });

      return {
        success: false,
        response: apiValidationError(fieldErrors),
      };
    }

    return {
      success: false,
      response: apiError("VALIDATION_ERROR", "Invalid request data"),
    };
  }
}

export async function validateJsonBody<T>(
  request: Request,
  schema: z.ZodSchema<T>
): Promise<{ success: true; data: T } | { success: false; response: Response }> {
  try {
    const body = await request.json();
    return validateRequest(schema, body);
  } catch (error) {
    return {
      success: false,
      response: apiError("INVALID_JSON", "Invalid JSON in request body"),
    };
  }
}
```

### Pagination Helper

**File: `core/api/pagination.ts`**
```typescript
import { PaginationParams } from "./types";

export function parsePaginationParams(
  searchParams: URLSearchParams
): PaginationParams {
  return {
    page: Math.max(1, parseInt(searchParams.get("page") || "1")),
    limit: Math.min(100, Math.max(1, parseInt(searchParams.get("limit") || "20"))),
    sortBy: searchParams.get("sortBy") || "createdAt",
    sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
  };
}

export function createPaginationMeta(
  page: number,
  limit: number,
  total: number
) {
  return {
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  };
}
```

### Health Check Endpoint

**File: `app/api/v1/health/route.ts`**
```typescript
import { NextRequest } from "next/server";
import { apiSuccess, apiError } from "@/core/api/response";
import { getDb } from "@/core/database";

export async function GET(request: NextRequest) {
  try {
    // Check database connection
    const db = getDb();
    await db.execute("SELECT 1");

    return apiSuccess({
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      services: {
        database: "connected",
        auth: "operational",
      },
    });
  } catch (error) {
    console.error("Health check failed:", error);
    return apiError(
      "HEALTH_CHECK_FAILED",
      "Service health check failed",
      503,
      { error: error instanceof Error ? error.message : "Unknown error" }
    );
  }
}
```

### Error Logging

**File: `core/api/logging.ts`**
```typescript
import { getDb } from "@/core/database";
import { activityLogs } from "@/core/database/schema";

export async function logApiActivity(
  organizationId: string,
  userId: string,
  action: string,
  entityType: string,
  entityId: string,
  details?: any
) {
  try {
    const db = getDb();
    await db.insert(activityLogs).values({
      organizationId,
      userId,
      entityType,
      entityId,
      action,
      newValues: JSON.stringify(details),
      ipAddress: "", // Will be populated from request
      userAgent: "", // Will be populated from request
    });
  } catch (error) {
    console.error("Failed to log API activity:", error);
  }
}
```

## Definition of Done

- [ ] API route structure is established under `/api/v1/`
- [ ] Response helpers provide consistent JSON responses
- [ ] Authentication middleware validates user sessions
- [ ] Error handling provides consistent error responses
- [ ] Validation helpers work with Zod schemas
- [ ] Pagination helpers support list endpoints
- [ ] Health check endpoint is functional
- [ ] Activity logging captures API usage
- [ ] User context is available in all protected endpoints
- [ ] Organization-based data isolation is enforced
- [ ] HTTP status codes are used correctly
- [ ] API versioning structure supports future versions
- [ ] Error responses include proper error codes and messages
- [ ] Request validation prevents invalid data processing
- [ ] Database errors are handled gracefully
