# Task 1: Project Management API

## Task Title
Implement comprehensive project management system with tasks, sprints, and time tracking

## Context
The project management system is the core operational component of the platform, enabling freelancers and teams to organize work, track progress, and deliver value to clients. This system must support agile methodologies with sprint planning, hierarchical task structures, time tracking, and comprehensive project lifecycle management.

This task creates the foundation for work organization that integrates with client relationships, team collaboration, and financial tracking while providing the flexibility needed for different project types and methodologies.

## Acceptance Criteria

1. **Project Lifecycle Management**
   - Create, read, update, and delete projects with proper validation
   - Project status tracking through defined workflow states
   - Project templates for consistent project creation
   - Project archiving and data retention policies

2. **Task Management System**
   - Hierarchical task structure with parent-child relationships
   - Custom task statuses per organization with workflow management
   - Task assignments to multiple team members
   - Task dependencies with different dependency types

3. **Sprint and Agile Support**
   - Sprint creation and management within projects
   - Task assignment to sprints with capacity planning
   - Sprint burndown tracking and reporting
   - Agile workflow support with customizable processes

4. **Time Tracking Integration**
   - Time entry creation and management for tasks
   - Estimated vs. actual time comparison
   - Time reporting and analysis
   - Billable vs. non-billable time categorization

## Dependencies
- Sprint 1: All tasks completed
- Sprint 2: Organization and User Management
- Sprint 3: Client Management API
- Project and task-related database tables

## Technical Requirements

### API Endpoints

**Project Management**
- `GET /api/v1/projects` - List organization projects
- `POST /api/v1/projects` - Create new project
- `GET /api/v1/projects/{id}` - Get project details
- `PUT /api/v1/projects/{id}` - Update project
- `DELETE /api/v1/projects/{id}` - Delete project

**Task Management**
- `GET /api/v1/projects/{id}/tasks` - List project tasks
- `POST /api/v1/projects/{id}/tasks` - Create new task
- `GET /api/v1/tasks/{id}` - Get task details
- `PUT /api/v1/tasks/{id}` - Update task
- `DELETE /api/v1/tasks/{id}` - Delete task

**Sprint Management**
- `GET /api/v1/projects/{id}/sprints` - List project sprints
- `POST /api/v1/projects/{id}/sprints` - Create new sprint
- `GET /api/v1/sprints/{id}` - Get sprint details
- `PUT /api/v1/sprints/{id}` - Update sprint

**Time Tracking**
- `GET /api/v1/tasks/{id}/time-entries` - List task time entries
- `POST /api/v1/tasks/{id}/time-entries` - Create time entry
- `PUT /api/v1/time-entries/{id}` - Update time entry
- `DELETE /api/v1/time-entries/{id}` - Delete time entry

### Data Models

**Project Creation Request**
```typescript
interface CreateProjectRequest {
  name: string;
  description?: string;
  clientId: string;
  status: "planning" | "active" | "on_hold" | "completed" | "cancelled";
  priority: "low" | "medium" | "high" | "urgent";
  startDate?: string;
  endDate?: string;
  budget?: number;
  currency?: string;
  billingType: "fixed" | "hourly" | "milestone";
  hourlyRate?: number;
  tags?: string[];
  teamMembers?: string[];
}
```

**Task Creation Request**
```typescript
interface CreateTaskRequest {
  title: string;
  description?: string;
  statusId: string;
  priority: "low" | "medium" | "high" | "urgent";
  estimatedHours?: number;
  parentTaskId?: string;
  sprintId?: string;
  assignees?: string[];
  dueDate?: string;
  tags?: string[];
}
```

### Implementation Files

**File: `app/api/v1/projects/route.ts`**
```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError } from "@/core/api/response";
import { validateJsonBody, parsePaginationParams } from "@/core/api/validation";
import { getDb } from "@/core/database";
import { 
  projects, 
  clients, 
  projectTasks, 
  projectSprints,
  projectTeamMembers 
} from "@/core/database/schema";
import { eq, and, count, sum } from "drizzle-orm";

const createProjectSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().max(2000).optional(),
  clientId: z.string().uuid(),
  status: z.enum(["planning", "active", "on_hold", "completed", "cancelled"]).default("planning"),
  priority: z.enum(["low", "medium", "high", "urgent"]).default("medium"),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  budget: z.number().positive().optional(),
  currency: z.string().length(3).default("USD"),
  billingType: z.enum(["fixed", "hourly", "milestone"]).default("hourly"),
  hourlyRate: z.number().positive().optional(),
  tags: z.array(z.string()).default([]),
  teamMembers: z.array(z.string().uuid()).default([]),
});

// GET /api/v1/projects - List organization projects
export const GET = withApiAuth(async (request, { user, organizationId }) => {
  const { searchParams } = new URL(request.url);
  const pagination = parsePaginationParams(searchParams);
  const status = searchParams.get("status");
  const clientId = searchParams.get("clientId");
  const search = searchParams.get("search");

  const db = getDb();

  let query = db
    .select({
      id: projects.id,
      name: projects.name,
      description: projects.description,
      status: projects.status,
      priority: projects.priority,
      startDate: projects.startDate,
      endDate: projects.endDate,
      budget: projects.budget,
      currency: projects.currency,
      billingType: projects.billingType,
      hourlyRate: projects.hourlyRate,
      createdAt: projects.createdAt,
      updatedAt: projects.updatedAt,
      clientName: clients.name,
      clientId: clients.id,
    })
    .from(projects)
    .innerJoin(clients, eq(projects.clientId, clients.id))
    .where(eq(projects.organizationId, organizationId));

  // Apply filters
  if (status) {
    query = query.where(and(
      eq(projects.organizationId, organizationId),
      eq(projects.status, status as any)
    ));
  }

  if (clientId) {
    query = query.where(and(
      eq(projects.organizationId, organizationId),
      eq(projects.clientId, clientId)
    ));
  }

  if (search) {
    query = query.where(and(
      eq(projects.organizationId, organizationId),
      or(
        ilike(projects.name, `%${search}%`),
        ilike(projects.description, `%${search}%`)
      )
    ));
  }

  // Apply pagination
  const offset = (pagination.page - 1) * pagination.limit;
  const projectList = await query
    .limit(pagination.limit)
    .offset(offset)
    .orderBy(projects[pagination.sortBy] || projects.createdAt);

  // Get total count
  const [{ total }] = await db
    .select({ total: count() })
    .from(projects)
    .where(eq(projects.organizationId, organizationId));

  // Enhance with task statistics
  const enhancedProjects = await Promise.all(
    projectList.map(async (project) => {
      const [taskStats] = await db
        .select({
          totalTasks: count(),
          completedTasks: count(projectTasks.statusId === "completed" ? 1 : undefined),
          totalEstimated: sum(projectTasks.estimatedHours),
          totalActual: sum(projectTasks.actualHours),
        })
        .from(projectTasks)
        .where(eq(projectTasks.projectId, project.id));

      return {
        ...project,
        stats: {
          totalTasks: taskStats?.totalTasks || 0,
          completedTasks: taskStats?.completedTasks || 0,
          totalEstimatedHours: Number(taskStats?.totalEstimated || 0),
          totalActualHours: Number(taskStats?.totalActual || 0),
          completionPercentage: taskStats?.totalTasks 
            ? Math.round((taskStats.completedTasks / taskStats.totalTasks) * 100)
            : 0,
        },
      };
    })
  );

  return apiSuccess(enhancedProjects, {
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total,
      totalPages: Math.ceil(total / pagination.limit),
    },
  });
}, { requireUserType: "internal", requireOrganization: true });

// POST /api/v1/projects - Create new project
export const POST = withApiAuth(async (request, { user, organizationId }) => {
  const validation = await validateJsonBody(request, createProjectSchema);
  if (!validation.success) {
    return validation.response;
  }

  const db = getDb();
  const projectId = crypto.randomUUID();

  try {
    // Verify client belongs to organization
    const [client] = await db
      .select()
      .from(clients)
      .where(
        and(
          eq(clients.id, validation.data.clientId),
          eq(clients.organizationId, organizationId)
        )
      );

    if (!client) {
      return apiError("INVALID_CLIENT", "Client not found", 400);
    }

    await db.transaction(async (tx) => {
      // Create project
      await tx.insert(projects).values({
        id: projectId,
        organizationId,
        ownerId: user.id,
        ...validation.data,
        startDate: validation.data.startDate ? new Date(validation.data.startDate) : undefined,
        endDate: validation.data.endDate ? new Date(validation.data.endDate) : undefined,
        teamMembers: undefined, // Handle separately
      });

      // Add team members
      if (validation.data.teamMembers.length > 0) {
        await tx.insert(projectTeamMembers).values(
          validation.data.teamMembers.map(userId => ({
            id: crypto.randomUUID(),
            projectId,
            userId,
            role: "member",
            joinedAt: new Date(),
          }))
        );
      }

      // Add project owner as team member
      await tx.insert(projectTeamMembers).values({
        id: crypto.randomUUID(),
        projectId,
        userId: user.id,
        role: "owner",
        joinedAt: new Date(),
      });
    });

    // Fetch created project with client info
    const [createdProject] = await db
      .select({
        id: projects.id,
        name: projects.name,
        description: projects.description,
        status: projects.status,
        priority: projects.priority,
        startDate: projects.startDate,
        endDate: projects.endDate,
        budget: projects.budget,
        currency: projects.currency,
        billingType: projects.billingType,
        hourlyRate: projects.hourlyRate,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
        clientName: clients.name,
        clientId: clients.id,
      })
      .from(projects)
      .innerJoin(clients, eq(projects.clientId, clients.id))
      .where(eq(projects.id, projectId));

    return apiSuccess(createdProject, { status: 201 });
  } catch (error) {
    console.error("Failed to create project:", error);
    return apiError("CREATION_FAILED", "Failed to create project", 500);
  }
}, { requireUserType: "internal", requireOrganization: true });
```

**File: `app/api/v1/projects/[id]/tasks/route.ts`**
```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError, apiNotFound } from "@/core/api/response";
import { validateJsonBody, parsePaginationParams } from "@/core/api/validation";
import { getDb } from "@/core/database";
import { 
  projectTasks, 
  projects, 
  taskStatuses,
  projectTaskAssignees,
  users 
} from "@/core/database/schema";
import { eq, and, isNull } from "drizzle-orm";

const createTaskSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().max(2000).optional(),
  statusId: z.string().uuid(),
  priority: z.enum(["low", "medium", "high", "urgent"]).default("medium"),
  estimatedHours: z.number().positive().optional(),
  parentTaskId: z.string().uuid().optional(),
  sprintId: z.string().uuid().optional(),
  assignees: z.array(z.string().uuid()).default([]),
  dueDate: z.string().datetime().optional(),
  tags: z.array(z.string()).default([]),
});

// GET /api/v1/projects/{id}/tasks - List project tasks
export const GET = withApiAuth(async (request, { user, organizationId }, { params }) => {
  const projectId = params.id;
  const { searchParams } = new URL(request.url);
  const pagination = parsePaginationParams(searchParams);
  const statusId = searchParams.get("statusId");
  const sprintId = searchParams.get("sprintId");
  const assigneeId = searchParams.get("assigneeId");
  const parentOnly = searchParams.get("parentOnly") === "true";

  const db = getDb();

  // Verify project access
  const [project] = await db
    .select()
    .from(projects)
    .where(
      and(
        eq(projects.id, projectId),
        eq(projects.organizationId, organizationId)
      )
    );

  if (!project) {
    return apiNotFound("Project");
  }

  let query = db
    .select({
      id: projectTasks.id,
      title: projectTasks.title,
      description: projectTasks.description,
      priority: projectTasks.priority,
      estimatedHours: projectTasks.estimatedHours,
      actualHours: projectTasks.actualHours,
      parentTaskId: projectTasks.parentTaskId,
      sprintId: projectTasks.sprintId,
      dueDate: projectTasks.dueDate,
      createdAt: projectTasks.createdAt,
      updatedAt: projectTasks.updatedAt,
      statusName: taskStatuses.name,
      statusColor: taskStatuses.color,
      statusId: taskStatuses.id,
    })
    .from(projectTasks)
    .innerJoin(taskStatuses, eq(projectTasks.statusId, taskStatuses.id))
    .where(eq(projectTasks.projectId, projectId));

  // Apply filters
  if (statusId) {
    query = query.where(and(
      eq(projectTasks.projectId, projectId),
      eq(projectTasks.statusId, statusId)
    ));
  }

  if (sprintId) {
    query = query.where(and(
      eq(projectTasks.projectId, projectId),
      eq(projectTasks.sprintId, sprintId)
    ));
  }

  if (parentOnly) {
    query = query.where(and(
      eq(projectTasks.projectId, projectId),
      isNull(projectTasks.parentTaskId)
    ));
  }

  // Apply pagination
  const offset = (pagination.page - 1) * pagination.limit;
  const taskList = await query
    .limit(pagination.limit)
    .offset(offset)
    .orderBy(projectTasks.createdAt);

  // Get assignees for each task
  const enhancedTasks = await Promise.all(
    taskList.map(async (task) => {
      const assignees = await db
        .select({
          id: users.id,
          name: users.name,
          email: users.email,
          avatar: users.avatar,
        })
        .from(projectTaskAssignees)
        .innerJoin(users, eq(projectTaskAssignees.userId, users.id))
        .where(eq(projectTaskAssignees.taskId, task.id));

      return {
        ...task,
        assignees,
      };
    })
  );

  return apiSuccess(enhancedTasks, {
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total: taskList.length,
      totalPages: Math.ceil(taskList.length / pagination.limit),
    },
  });
}, { requireUserType: "internal", requireOrganization: true });

// POST /api/v1/projects/{id}/tasks - Create new task
export const POST = withApiAuth(async (request, { user, organizationId }, { params }) => {
  const projectId = params.id;
  const validation = await validateJsonBody(request, createTaskSchema);
  
  if (!validation.success) {
    return validation.response;
  }

  const db = getDb();
  const taskId = crypto.randomUUID();

  try {
    // Verify project access
    const [project] = await db
      .select()
      .from(projects)
      .where(
        and(
          eq(projects.id, projectId),
          eq(projects.organizationId, organizationId)
        )
      );

    if (!project) {
      return apiNotFound("Project");
    }

    // Verify status belongs to organization
    const [status] = await db
      .select()
      .from(taskStatuses)
      .where(
        and(
          eq(taskStatuses.id, validation.data.statusId),
          eq(taskStatuses.organizationId, organizationId)
        )
      );

    if (!status) {
      return apiError("INVALID_STATUS", "Task status not found", 400);
    }

    await db.transaction(async (tx) => {
      // Create task
      await tx.insert(projectTasks).values({
        id: taskId,
        projectId,
        ...validation.data,
        dueDate: validation.data.dueDate ? new Date(validation.data.dueDate) : undefined,
        assignees: undefined, // Handle separately
      });

      // Add assignees
      if (validation.data.assignees.length > 0) {
        await tx.insert(projectTaskAssignees).values(
          validation.data.assignees.map(userId => ({
            id: crypto.randomUUID(),
            taskId,
            userId,
            assignedAt: new Date(),
          }))
        );
      }
    });

    // Fetch created task with status and assignees
    const [createdTask] = await db
      .select({
        id: projectTasks.id,
        title: projectTasks.title,
        description: projectTasks.description,
        priority: projectTasks.priority,
        estimatedHours: projectTasks.estimatedHours,
        actualHours: projectTasks.actualHours,
        parentTaskId: projectTasks.parentTaskId,
        sprintId: projectTasks.sprintId,
        dueDate: projectTasks.dueDate,
        createdAt: projectTasks.createdAt,
        updatedAt: projectTasks.updatedAt,
        statusName: taskStatuses.name,
        statusColor: taskStatuses.color,
        statusId: taskStatuses.id,
      })
      .from(projectTasks)
      .innerJoin(taskStatuses, eq(projectTasks.statusId, taskStatuses.id))
      .where(eq(projectTasks.id, taskId));

    return apiSuccess(createdTask, { status: 201 });
  } catch (error) {
    console.error("Failed to create task:", error);
    return apiError("CREATION_FAILED", "Failed to create task", 500);
  }
}, { requireUserType: "internal", requireOrganization: true });
```

## Definition of Done

- [ ] Project CRUD operations are fully implemented and functional
- [ ] Task management supports hierarchical structures and dependencies
- [ ] Sprint management enables agile project organization
- [ ] Time tracking captures estimated vs. actual hours
- [ ] Project status workflow is properly implemented
- [ ] Task assignments support multiple team members
- [ ] Custom task statuses work per organization
- [ ] Project statistics provide meaningful insights
- [ ] API endpoints support pagination and filtering
- [ ] Project search functionality works across name and description
- [ ] Data isolation ensures proper multi-tenant security
- [ ] Error handling provides clear feedback for all operations
- [ ] Validation ensures data integrity for all project operations
- [ ] Activity logging captures all project-related changes
- [ ] Performance is optimized for project and task queries
