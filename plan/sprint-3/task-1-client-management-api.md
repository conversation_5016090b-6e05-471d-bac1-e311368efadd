# Task 1: Client Management API

## Task Title
Implement comprehensive client management system with portal access and multi-contact support

## Context
The client management system is a core differentiator of the platform, providing sophisticated client relationship management with integrated portal access. This system must handle complex client structures including multiple contacts, billing addresses, preferences, and the unified authentication system that allows clients to access their project information directly.

This task creates the foundation for client relationships that will integrate with projects, invoices, and communication systems while maintaining proper data isolation and security.

## Acceptance Criteria

1. **Client CRUD Operations**
   - Create, read, update, and delete client records
   - Support for complex client information including company details
   - Multiple contact persons per client with role designations
   - Billing address management with multiple address support

2. **Client Portal Integration**
   - Client users can be created and linked to client records
   - Portal access allows clients to view their projects and invoices
   - Proper authentication and authorization for client users
   - Client-specific data filtering and access control

3. **Client Preferences and Settings**
   - Communication preferences (email, phone, portal notifications)
   - Project visibility and access controls
   - Timezone and language preferences
   - Custom client-specific settings

4. **Client Relationship Management**
   - Client status tracking (active, inactive, prospect)
   - Client categorization and tagging
   - Client history and activity tracking
   - Integration with project and invoice systems

## Dependencies
- Sprint 1: All tasks completed
- Sprint 2: Organization and User Management
- Better Auth unified user system
- Client-related database tables

## Technical Requirements

### API Endpoints

**Client Management**
- `GET /api/v1/clients` - List organization clients
- `POST /api/v1/clients` - Create new client
- `GET /api/v1/clients/{id}` - Get client details
- `PUT /api/v1/clients/{id}` - Update client
- `DELETE /api/v1/clients/{id}` - Delete client

**Client Contacts**
- `GET /api/v1/clients/{id}/contacts` - List client contacts
- `POST /api/v1/clients/{id}/contacts` - Add client contact
- `PUT /api/v1/clients/{id}/contacts/{contactId}` - Update contact
- `DELETE /api/v1/clients/{id}/contacts/{contactId}` - Remove contact

**Client Portal**
- `POST /api/v1/clients/{id}/portal/invite` - Invite client to portal
- `GET /api/v1/clients/{id}/portal/access` - Get portal access info
- `PUT /api/v1/clients/{id}/preferences` - Update client preferences

### Data Models

**Client Creation Request**
```typescript
interface CreateClientRequest {
  name: string;
  type: "individual" | "company";
  email?: string;
  phone?: string;
  website?: string;
  industry?: string;
  description?: string;
  status: "prospect" | "active" | "inactive";
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  contacts?: Array<{
    name: string;
    email: string;
    phone?: string;
    role?: string;
    isPrimary?: boolean;
  }>;
}
```

**Client Response**
```typescript
interface ClientResponse {
  id: string;
  name: string;
  type: "individual" | "company";
  email?: string;
  phone?: string;
  website?: string;
  industry?: string;
  description?: string;
  status: "prospect" | "active" | "inactive";
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  contacts: Array<{
    id: string;
    name: string;
    email: string;
    phone?: string;
    role?: string;
    isPrimary: boolean;
  }>;
  preferences?: {
    timezone: string;
    language: string;
    emailNotifications: boolean;
    portalAccess: boolean;
  };
  stats: {
    projectCount: number;
    activeProjectCount: number;
    totalInvoiced: number;
    outstandingAmount: number;
  };
}
```

### Implementation Files

**File: `app/api/v1/clients/route.ts`**
```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError } from "@/core/api/response";
import { validateJsonBody, parsePaginationParams } from "@/core/api/validation";
import { getDb } from "@/core/database";
import { clients, clientContacts, clientBillingAddress } from "@/core/database/schema";
import { eq, and, count, sum } from "drizzle-orm";

const createClientSchema = z.object({
  name: z.string().min(1).max(255),
  type: z.enum(["individual", "company"]),
  email: z.string().email().optional(),
  phone: z.string().max(20).optional(),
  website: z.string().url().optional(),
  industry: z.string().max(100).optional(),
  description: z.string().max(1000).optional(),
  status: z.enum(["prospect", "active", "inactive"]).default("prospect"),
  billingAddress: z.object({
    street: z.string().min(1),
    city: z.string().min(1),
    state: z.string().min(1),
    postalCode: z.string().min(1),
    country: z.string().min(1),
  }).optional(),
  contacts: z.array(z.object({
    name: z.string().min(1),
    email: z.string().email(),
    phone: z.string().optional(),
    role: z.string().optional(),
    isPrimary: z.boolean().default(false),
  })).optional(),
});

// GET /api/v1/clients - List organization clients
export const GET = withApiAuth(async (request, { user, organizationId }) => {
  const { searchParams } = new URL(request.url);
  const pagination = parsePaginationParams(searchParams);
  const status = searchParams.get("status");
  const search = searchParams.get("search");

  const db = getDb();

  let query = db
    .select({
      id: clients.id,
      name: clients.name,
      type: clients.type,
      email: clients.email,
      phone: clients.phone,
      website: clients.website,
      industry: clients.industry,
      status: clients.status,
      isActive: clients.isActive,
      createdAt: clients.createdAt,
      updatedAt: clients.updatedAt,
    })
    .from(clients)
    .where(eq(clients.organizationId, organizationId));

  // Apply filters
  if (status) {
    query = query.where(and(
      eq(clients.organizationId, organizationId),
      eq(clients.status, status as any)
    ));
  }

  if (search) {
    query = query.where(and(
      eq(clients.organizationId, organizationId),
      or(
        ilike(clients.name, `%${search}%`),
        ilike(clients.email, `%${search}%`)
      )
    ));
  }

  // Apply pagination
  const offset = (pagination.page - 1) * pagination.limit;
  const clientList = await query
    .limit(pagination.limit)
    .offset(offset)
    .orderBy(clients[pagination.sortBy] || clients.createdAt);

  // Get total count
  const [{ total }] = await db
    .select({ total: count() })
    .from(clients)
    .where(eq(clients.organizationId, organizationId));

  return apiSuccess(clientList, {
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total,
      totalPages: Math.ceil(total / pagination.limit),
    },
  });
}, { requireUserType: "internal", requireOrganization: true });

// POST /api/v1/clients - Create new client
export const POST = withApiAuth(async (request, { user, organizationId }) => {
  const validation = await validateJsonBody(request, createClientSchema);
  if (!validation.success) {
    return validation.response;
  }

  const db = getDb();
  const clientId = crypto.randomUUID();

  try {
    await db.transaction(async (tx) => {
      // Create client
      await tx.insert(clients).values({
        id: clientId,
        organizationId,
        ownerId: user.id,
        ...validation.data,
        billingAddress: undefined, // Handle separately
        contacts: undefined, // Handle separately
      });

      // Create billing address if provided
      if (validation.data.billingAddress) {
        await tx.insert(clientBillingAddress).values({
          clientId,
          ...validation.data.billingAddress,
        });
      }

      // Create contacts if provided
      if (validation.data.contacts?.length) {
        await tx.insert(clientContacts).values(
          validation.data.contacts.map(contact => ({
            id: crypto.randomUUID(),
            clientId,
            ...contact,
          }))
        );
      }
    });

    // Fetch created client with all related data
    const [createdClient] = await db
      .select()
      .from(clients)
      .where(eq(clients.id, clientId));

    return apiSuccess(createdClient, { status: 201 });
  } catch (error) {
    console.error("Failed to create client:", error);
    return apiError("CREATION_FAILED", "Failed to create client", 500);
  }
}, { requireUserType: "internal", requireOrganization: true });
```

**File: `app/api/v1/clients/[id]/route.ts`**
```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError, apiNotFound } from "@/core/api/response";
import { validateJsonBody } from "@/core/api/validation";
import { getDb } from "@/core/database";
import { 
  clients, 
  clientContacts, 
  clientBillingAddress, 
  clientPreferences,
  projects,
  invoices 
} from "@/core/database/schema";
import { eq, and, count, sum } from "drizzle-orm";

// GET /api/v1/clients/{id} - Get client details
export const GET = withApiAuth(async (request, { user, organizationId }, { params }) => {
  const clientId = params.id;
  const db = getDb();

  // Get client details
  const [client] = await db
    .select()
    .from(clients)
    .where(
      and(
        eq(clients.id, clientId),
        eq(clients.organizationId, organizationId)
      )
    );

  if (!client) {
    return apiNotFound("Client");
  }

  // Get billing address
  const [billingAddress] = await db
    .select()
    .from(clientBillingAddress)
    .where(eq(clientBillingAddress.clientId, clientId));

  // Get contacts
  const contacts = await db
    .select()
    .from(clientContacts)
    .where(eq(clientContacts.clientId, clientId))
    .orderBy(clientContacts.isPrimary, clientContacts.name);

  // Get preferences
  const [preferences] = await db
    .select()
    .from(clientPreferences)
    .where(eq(clientPreferences.clientId, clientId));

  // Get client statistics
  const [projectStats] = await db
    .select({
      total: count(),
      active: count(projects.status === "active" ? 1 : undefined),
    })
    .from(projects)
    .where(eq(projects.clientId, clientId));

  const [invoiceStats] = await db
    .select({
      totalInvoiced: sum(invoices.totalAmount),
      outstanding: sum(
        invoices.status === "sent" || invoices.status === "overdue" 
          ? invoices.totalAmount 
          : 0
      ),
    })
    .from(invoices)
    .where(eq(invoices.clientId, clientId));

  const response: ClientResponse = {
    id: client.id,
    name: client.name,
    type: client.type,
    email: client.email,
    phone: client.phone,
    website: client.website,
    industry: client.industry,
    description: client.description,
    status: client.status,
    isActive: client.isActive,
    createdAt: client.createdAt.toISOString(),
    updatedAt: client.updatedAt.toISOString(),
    billingAddress: billingAddress ? {
      street: billingAddress.street,
      city: billingAddress.city,
      state: billingAddress.state,
      postalCode: billingAddress.postalCode,
      country: billingAddress.country,
    } : undefined,
    contacts: contacts.map(contact => ({
      id: contact.id,
      name: contact.name,
      email: contact.email,
      phone: contact.phone,
      role: contact.role,
      isPrimary: contact.isPrimary,
    })),
    preferences: preferences ? {
      timezone: preferences.timezone,
      language: preferences.language,
      emailNotifications: preferences.emailNotifications,
      portalAccess: preferences.portalAccess,
    } : undefined,
    stats: {
      projectCount: projectStats?.total || 0,
      activeProjectCount: projectStats?.active || 0,
      totalInvoiced: Number(invoiceStats?.totalInvoiced || 0),
      outstandingAmount: Number(invoiceStats?.outstanding || 0),
    },
  };

  return apiSuccess(response);
}, { requireUserType: "internal", requireOrganization: true });
```

**File: `app/api/v1/clients/[id]/portal/invite/route.ts`**
```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError, apiNotFound } from "@/core/api/response";
import { validateJsonBody } from "@/core/api/validation";
import { getDb } from "@/core/database";
import { clients, users } from "@/core/database/schema";
import { eq, and } from "drizzle-orm";
import { auth } from "@/core/auth/server";

const inviteClientSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1),
  sendInvitation: z.boolean().default(true),
});

// POST /api/v1/clients/{id}/portal/invite - Invite client to portal
export const POST = withApiAuth(async (request, { user, organizationId }, { params }) => {
  const clientId = params.id;
  const validation = await validateJsonBody(request, inviteClientSchema);
  
  if (!validation.success) {
    return validation.response;
  }

  const db = getDb();

  // Verify client exists and belongs to organization
  const [client] = await db
    .select()
    .from(clients)
    .where(
      and(
        eq(clients.id, clientId),
        eq(clients.organizationId, organizationId)
      )
    );

  if (!client) {
    return apiNotFound("Client");
  }

  try {
    // Check if user already exists
    const [existingUser] = await db
      .select()
      .from(users)
      .where(eq(users.email, validation.data.email));

    if (existingUser) {
      // Link existing user to client if not already linked
      if (existingUser.clientId !== clientId) {
        await db
          .update(users)
          .set({ 
            clientId,
            organizationId,
            userType: "client",
          })
          .where(eq(users.id, existingUser.id));
      }

      return apiSuccess({
        message: "User already exists and has been linked to client",
        userId: existingUser.id,
        isNewUser: false,
      });
    }

    // Create new client user
    const newUser = await auth.api.signUpEmail({
      email: validation.data.email,
      password: crypto.randomUUID(), // Temporary password
      name: validation.data.name,
      body: {
        userType: "client",
        clientId,
        organizationId,
        requirePasswordReset: true,
      },
    });

    if (validation.data.sendInvitation) {
      // TODO: Send invitation email with portal access instructions
      // This would integrate with email service in future sprint
    }

    return apiSuccess({
      message: "Client user created successfully",
      userId: newUser.user.id,
      isNewUser: true,
      invitationSent: validation.data.sendInvitation,
    }, { status: 201 });
  } catch (error) {
    console.error("Failed to invite client:", error);
    return apiError("INVITATION_FAILED", "Failed to invite client", 500);
  }
}, { requireUserType: "internal", requireOrganization: true });
```

## Definition of Done

- [ ] Client CRUD operations are fully implemented and functional
- [ ] Multiple contact support allows complex client relationship management
- [ ] Billing address management supports multiple addresses per client
- [ ] Client portal invitation system creates and links client users
- [ ] Client preferences and settings are properly managed
- [ ] Client status tracking and categorization work correctly
- [ ] Client statistics provide meaningful insights (projects, invoices)
- [ ] Data isolation ensures clients only see their own data
- [ ] API endpoints support pagination and filtering
- [ ] Client search functionality works across name and email
- [ ] Error handling provides clear feedback for all operations
- [ ] Validation ensures data integrity for all client operations
- [ ] Activity logging captures all client-related changes
- [ ] Performance is optimized for client list and detail queries
- [ ] Security measures prevent unauthorized client access
