# Task 1: Invoice Management API

## Task Title
Implement comprehensive invoice management system with automated generation and payment tracking

## Context
The invoice management system is critical for the platform's financial functionality, enabling freelancers and agencies to generate professional invoices, track payments, and manage their cash flow. This system must integrate with projects and time tracking while providing flexible billing options and automated workflows.

This task creates the foundation for financial management that will integrate with client relationships, project billing, and payment processing while maintaining proper audit trails and compliance requirements.

## Acceptance Criteria

1. **Invoice Lifecycle Management**
   - Create, read, update, and delete invoices with proper validation
   - Invoice status tracking through defined workflow states
   - Automated invoice generation from project time entries
   - Invoice templates and customization options

2. **Flexible Billing Support**
   - Fixed-price project billing
   - Hourly rate billing with time entry integration
   - Milestone-based billing
   - Recurring invoice support for retainer clients

3. **Payment Tracking**
   - Payment recording and reconciliation
   - Partial payment support
   - Payment reminder automation
   - Late fee calculation and application

4. **Financial Reporting**
   - Invoice aging reports
   - Revenue tracking and analysis
   - Outstanding balance monitoring
   - Tax reporting preparation

## Dependencies
- Sprint 1: All tasks completed
- Sprint 2: Organization and User Management
- Sprint 3: Client Management API
- Sprint 4: Project Management API
- Invoice-related database tables

## Technical Requirements

### API Endpoints

**Invoice Management**
- `GET /api/v1/invoices` - List organization invoices
- `POST /api/v1/invoices` - Create new invoice
- `GET /api/v1/invoices/{id}` - Get invoice details
- `PUT /api/v1/invoices/{id}` - Update invoice
- `DELETE /api/v1/invoices/{id}` - Delete invoice

**Invoice Generation**
- `POST /api/v1/projects/{id}/generate-invoice` - Generate invoice from project
- `POST /api/v1/invoices/{id}/send` - Send invoice to client
- `POST /api/v1/invoices/{id}/duplicate` - Duplicate existing invoice

**Payment Management**
- `GET /api/v1/invoices/{id}/payments` - List invoice payments
- `POST /api/v1/invoices/{id}/payments` - Record payment
- `PUT /api/v1/payments/{id}` - Update payment
- `DELETE /api/v1/payments/{id}` - Delete payment

**Invoice Reports**
- `GET /api/v1/reports/invoices/aging` - Invoice aging report
- `GET /api/v1/reports/invoices/revenue` - Revenue analysis
- `GET /api/v1/reports/invoices/outstanding` - Outstanding balances

### Data Models

**Invoice Creation Request**
```typescript
interface CreateInvoiceRequest {
  clientId: string;
  projectId?: string;
  invoiceNumber?: string;
  title: string;
  description?: string;
  issueDate: string;
  dueDate: string;
  currency: string;
  taxRate?: number;
  discountAmount?: number;
  discountPercentage?: number;
  notes?: string;
  terms?: string;
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    amount: number;
    taxable?: boolean;
  }>;
}
```

**Invoice Response**
```typescript
interface InvoiceResponse {
  id: string;
  invoiceNumber: string;
  title: string;
  description?: string;
  status: "draft" | "sent" | "viewed" | "paid" | "overdue" | "cancelled";
  issueDate: string;
  dueDate: string;
  paidDate?: string;
  currency: string;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  balanceAmount: number;
  notes?: string;
  terms?: string;
  createdAt: string;
  updatedAt: string;
  client: {
    id: string;
    name: string;
    email: string;
  };
  project?: {
    id: string;
    name: string;
  };
  items: Array<{
    id: string;
    description: string;
    quantity: number;
    unitPrice: number;
    amount: number;
    taxable: boolean;
  }>;
  payments: Array<{
    id: string;
    amount: number;
    paymentDate: string;
    method: string;
    reference?: string;
  }>;
}
```

### Implementation Files

**File: `app/api/v1/invoices/route.ts`**
```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError } from "@/core/api/response";
import { validateJsonBody, parsePaginationParams } from "@/core/api/validation";
import { getDb } from "@/core/database";
import { 
  invoices, 
  invoiceItems, 
  invoicePayments,
  clients, 
  projects 
} from "@/core/database/schema";
import { eq, and, gte, lte, sum } from "drizzle-orm";

const createInvoiceSchema = z.object({
  clientId: z.string().uuid(),
  projectId: z.string().uuid().optional(),
  invoiceNumber: z.string().optional(),
  title: z.string().min(1).max(255),
  description: z.string().max(1000).optional(),
  issueDate: z.string().datetime(),
  dueDate: z.string().datetime(),
  currency: z.string().length(3).default("USD"),
  taxRate: z.number().min(0).max(100).optional(),
  discountAmount: z.number().min(0).optional(),
  discountPercentage: z.number().min(0).max(100).optional(),
  notes: z.string().max(1000).optional(),
  terms: z.string().max(2000).optional(),
  items: z.array(z.object({
    description: z.string().min(1),
    quantity: z.number().positive(),
    unitPrice: z.number().min(0),
    amount: z.number().min(0),
    taxable: z.boolean().default(true),
  })).min(1),
});

// GET /api/v1/invoices - List organization invoices
export const GET = withApiAuth(async (request, { user, organizationId }) => {
  const { searchParams } = new URL(request.url);
  const pagination = parsePaginationParams(searchParams);
  const status = searchParams.get("status");
  const clientId = searchParams.get("clientId");
  const projectId = searchParams.get("projectId");
  const fromDate = searchParams.get("fromDate");
  const toDate = searchParams.get("toDate");

  const db = getDb();

  let query = db
    .select({
      id: invoices.id,
      invoiceNumber: invoices.invoiceNumber,
      title: invoices.title,
      status: invoices.status,
      issueDate: invoices.issueDate,
      dueDate: invoices.dueDate,
      paidDate: invoices.paidDate,
      currency: invoices.currency,
      subtotal: invoices.subtotal,
      taxAmount: invoices.taxAmount,
      discountAmount: invoices.discountAmount,
      totalAmount: invoices.totalAmount,
      paidAmount: invoices.paidAmount,
      balanceAmount: invoices.balanceAmount,
      createdAt: invoices.createdAt,
      updatedAt: invoices.updatedAt,
      clientName: clients.name,
      clientId: clients.id,
      projectName: projects.name,
      projectId: projects.id,
    })
    .from(invoices)
    .innerJoin(clients, eq(invoices.clientId, clients.id))
    .leftJoin(projects, eq(invoices.projectId, projects.id))
    .where(eq(invoices.organizationId, organizationId));

  // Apply filters
  if (status) {
    query = query.where(and(
      eq(invoices.organizationId, organizationId),
      eq(invoices.status, status as any)
    ));
  }

  if (clientId) {
    query = query.where(and(
      eq(invoices.organizationId, organizationId),
      eq(invoices.clientId, clientId)
    ));
  }

  if (projectId) {
    query = query.where(and(
      eq(invoices.organizationId, organizationId),
      eq(invoices.projectId, projectId)
    ));
  }

  if (fromDate) {
    query = query.where(and(
      eq(invoices.organizationId, organizationId),
      gte(invoices.issueDate, new Date(fromDate))
    ));
  }

  if (toDate) {
    query = query.where(and(
      eq(invoices.organizationId, organizationId),
      lte(invoices.issueDate, new Date(toDate))
    ));
  }

  // Apply pagination
  const offset = (pagination.page - 1) * pagination.limit;
  const invoiceList = await query
    .limit(pagination.limit)
    .offset(offset)
    .orderBy(invoices[pagination.sortBy] || invoices.createdAt);

  // Get total count
  const [{ total }] = await db
    .select({ total: count() })
    .from(invoices)
    .where(eq(invoices.organizationId, organizationId));

  return apiSuccess(invoiceList, {
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total,
      totalPages: Math.ceil(total / pagination.limit),
    },
  });
}, { requireUserType: "internal", requireOrganization: true });

// POST /api/v1/invoices - Create new invoice
export const POST = withApiAuth(async (request, { user, organizationId }) => {
  const validation = await validateJsonBody(request, createInvoiceSchema);
  if (!validation.success) {
    return validation.response;
  }

  const db = getDb();
  const invoiceId = crypto.randomUUID();

  try {
    // Verify client belongs to organization
    const [client] = await db
      .select()
      .from(clients)
      .where(
        and(
          eq(clients.id, validation.data.clientId),
          eq(clients.organizationId, organizationId)
        )
      );

    if (!client) {
      return apiError("INVALID_CLIENT", "Client not found", 400);
    }

    // Generate invoice number if not provided
    let invoiceNumber = validation.data.invoiceNumber;
    if (!invoiceNumber) {
      const [lastInvoice] = await db
        .select({ invoiceNumber: invoices.invoiceNumber })
        .from(invoices)
        .where(eq(invoices.organizationId, organizationId))
        .orderBy(invoices.createdAt)
        .limit(1);

      const lastNumber = lastInvoice?.invoiceNumber 
        ? parseInt(lastInvoice.invoiceNumber.replace(/\D/g, '')) || 0
        : 0;
      
      invoiceNumber = `INV-${String(lastNumber + 1).padStart(4, '0')}`;
    }

    // Calculate totals
    const subtotal = validation.data.items.reduce((sum, item) => sum + item.amount, 0);
    const discountAmount = validation.data.discountAmount || 
      (validation.data.discountPercentage ? subtotal * (validation.data.discountPercentage / 100) : 0);
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = validation.data.taxRate ? taxableAmount * (validation.data.taxRate / 100) : 0;
    const totalAmount = taxableAmount + taxAmount;

    await db.transaction(async (tx) => {
      // Create invoice
      await tx.insert(invoices).values({
        id: invoiceId,
        organizationId,
        clientId: validation.data.clientId,
        projectId: validation.data.projectId,
        invoiceNumber,
        title: validation.data.title,
        description: validation.data.description,
        status: "draft",
        issueDate: new Date(validation.data.issueDate),
        dueDate: new Date(validation.data.dueDate),
        currency: validation.data.currency,
        subtotal,
        taxAmount,
        discountAmount,
        totalAmount,
        paidAmount: 0,
        balanceAmount: totalAmount,
        notes: validation.data.notes,
        terms: validation.data.terms,
      });

      // Create invoice items
      await tx.insert(invoiceItems).values(
        validation.data.items.map((item, index) => ({
          id: crypto.randomUUID(),
          invoiceId,
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          amount: item.amount,
          taxable: item.taxable,
          order: index + 1,
        }))
      );
    });

    // Fetch created invoice with client info
    const [createdInvoice] = await db
      .select({
        id: invoices.id,
        invoiceNumber: invoices.invoiceNumber,
        title: invoices.title,
        status: invoices.status,
        issueDate: invoices.issueDate,
        dueDate: invoices.dueDate,
        currency: invoices.currency,
        subtotal: invoices.subtotal,
        taxAmount: invoices.taxAmount,
        discountAmount: invoices.discountAmount,
        totalAmount: invoices.totalAmount,
        paidAmount: invoices.paidAmount,
        balanceAmount: invoices.balanceAmount,
        createdAt: invoices.createdAt,
        updatedAt: invoices.updatedAt,
        clientName: clients.name,
        clientId: clients.id,
      })
      .from(invoices)
      .innerJoin(clients, eq(invoices.clientId, clients.id))
      .where(eq(invoices.id, invoiceId));

    return apiSuccess(createdInvoice, { status: 201 });
  } catch (error) {
    console.error("Failed to create invoice:", error);
    return apiError("CREATION_FAILED", "Failed to create invoice", 500);
  }
}, { requireUserType: "internal", requireOrganization: true });
```

**File: `app/api/v1/invoices/[id]/payments/route.ts`**
```typescript
import { NextRequest } from "next/server";
import { z } from "zod";
import { withApiAuth } from "@/core/api/middleware";
import { apiSuccess, apiError, apiNotFound } from "@/core/api/response";
import { validateJsonBody } from "@/core/api/validation";
import { getDb } from "@/core/database";
import { invoices, invoicePayments } from "@/core/database/schema";
import { eq, and, sum } from "drizzle-orm";

const createPaymentSchema = z.object({
  amount: z.number().positive(),
  paymentDate: z.string().datetime(),
  method: z.enum(["cash", "check", "bank_transfer", "credit_card", "paypal", "stripe", "other"]),
  reference: z.string().max(255).optional(),
  notes: z.string().max(1000).optional(),
});

// GET /api/v1/invoices/{id}/payments - List invoice payments
export const GET = withApiAuth(async (request, { user, organizationId }, { params }) => {
  const invoiceId = params.id;
  const db = getDb();

  // Verify invoice access
  const [invoice] = await db
    .select()
    .from(invoices)
    .where(
      and(
        eq(invoices.id, invoiceId),
        eq(invoices.organizationId, organizationId)
      )
    );

  if (!invoice) {
    return apiNotFound("Invoice");
  }

  const payments = await db
    .select()
    .from(invoicePayments)
    .where(eq(invoicePayments.invoiceId, invoiceId))
    .orderBy(invoicePayments.paymentDate);

  return apiSuccess(payments);
}, { requireUserType: "internal", requireOrganization: true });

// POST /api/v1/invoices/{id}/payments - Record payment
export const POST = withApiAuth(async (request, { user, organizationId }, { params }) => {
  const invoiceId = params.id;
  const validation = await validateJsonBody(request, createPaymentSchema);
  
  if (!validation.success) {
    return validation.response;
  }

  const db = getDb();

  try {
    // Verify invoice access and get current balance
    const [invoice] = await db
      .select()
      .from(invoices)
      .where(
        and(
          eq(invoices.id, invoiceId),
          eq(invoices.organizationId, organizationId)
        )
      );

    if (!invoice) {
      return apiNotFound("Invoice");
    }

    // Validate payment amount doesn't exceed balance
    if (validation.data.amount > invoice.balanceAmount) {
      return apiError(
        "INVALID_AMOUNT", 
        "Payment amount exceeds invoice balance", 
        400
      );
    }

    const paymentId = crypto.randomUUID();
    const newPaidAmount = invoice.paidAmount + validation.data.amount;
    const newBalanceAmount = invoice.totalAmount - newPaidAmount;
    const newStatus = newBalanceAmount <= 0 ? "paid" : invoice.status;

    await db.transaction(async (tx) => {
      // Create payment record
      await tx.insert(invoicePayments).values({
        id: paymentId,
        invoiceId,
        amount: validation.data.amount,
        paymentDate: new Date(validation.data.paymentDate),
        method: validation.data.method,
        reference: validation.data.reference,
        notes: validation.data.notes,
      });

      // Update invoice totals and status
      await tx
        .update(invoices)
        .set({
          paidAmount: newPaidAmount,
          balanceAmount: newBalanceAmount,
          status: newStatus,
          paidDate: newBalanceAmount <= 0 ? new Date() : null,
          updatedAt: new Date(),
        })
        .where(eq(invoices.id, invoiceId));
    });

    // Fetch created payment
    const [createdPayment] = await db
      .select()
      .from(invoicePayments)
      .where(eq(invoicePayments.id, paymentId));

    return apiSuccess(createdPayment, { status: 201 });
  } catch (error) {
    console.error("Failed to record payment:", error);
    return apiError("PAYMENT_FAILED", "Failed to record payment", 500);
  }
}, { requireUserType: "internal", requireOrganization: true });
```

## Definition of Done

- [ ] Invoice CRUD operations are fully implemented and functional
- [ ] Invoice generation from projects and time entries works correctly
- [ ] Payment recording and tracking is accurate and reliable
- [ ] Invoice status workflow progresses correctly through all states
- [ ] Automated invoice numbering prevents duplicates
- [ ] Tax calculations are accurate for different tax rates
- [ ] Discount calculations work for both fixed amounts and percentages
- [ ] Payment reconciliation updates invoice balances correctly
- [ ] Invoice aging and overdue detection is functional
- [ ] API endpoints support comprehensive filtering and pagination
- [ ] Data isolation ensures proper multi-tenant security
- [ ] Error handling provides clear feedback for all operations
- [ ] Validation ensures financial data integrity
- [ ] Activity logging captures all invoice and payment changes
- [ ] Performance is optimized for invoice list and detail queries
